import type { <PERSON>ada<PERSON> } from "next";
import { Poppins } from "next/font/google";
import { Analytics } from '@vercel/analytics/next';
import { SpeedInsights } from '@vercel/speed-insights/next';
import ErrorBoundary from "../components/ErrorBoundary";
import CookieConsent from "../components/CookieConsent";
import "./globals.css";

const poppins = Poppins({
  weight: ["400", "600", "700"],
  subsets: ["latin"],
  variable: "--font-poppins",
  display: "swap",
});

const showCookieConsent = true;

export const metadata: Metadata = {
  title: "Sonata Sites | Fast & Affordable Website Building",
  description:
    "Sonata Sites offers rapid, affordable website design and development services tailored for small businesses, freelancers, and entrepreneurs. Get your custom site online fast with expert support and sleek design.",
  keywords: [
    "Sonata Sites",
    "website builder",
    "affordable web design",
    "fast website development",
    "custom websites",
    "freelancer websites",
    "small business website",
    "cheap websites",
    "web design service",
    "professional site builder",
  ],
  authors: [{ name: "Sonata Sites Team", url: "https://sonatasites.com" }],
  creator: "Sonata Sites",
  openGraph: {
    title: "Sonata Sites | Fast & Affordable Website Building",
    description:
      "Need a website fast? Sonata Sites delivers sleek, modern websites at affordable prices. Perfect for freelancers, tutors, and local businesses.",
    url: "https://sonatasites.com",
    siteName: "Sonata Sites",
    images: [
      {
        url: "https://sonata-assets.s3.us-east-2.amazonaws.com/sonata-open-graphic.png", // Replace with actual OG image URL
        width: 1200,
        height: 630,
        alt: "Sonata Sites homepage preview",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Sonata Sites",
    description:
      "Fast, affordable websites for freelancers, local businesses, and creators.",
    images: ["https://sonata-assets.s3.us-east-2.amazonaws.com/sonata-open-graphic.png"], // Replace with actual image
  },
  robots: {
    index: true,
    follow: true,
    nocache: false,
  },
};


export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/icon.png" type="image/png" />
        <meta name="theme-color" content="#ffffff" />
      </head>
      <body className={`${poppins.variable} font-poppins antialiased`}>
        <ErrorBoundary>
          {children}
          {showCookieConsent && <CookieConsent />}
          <SpeedInsights />
          <Analytics />
        </ErrorBoundary>
      </body>
    </html>
  );
}

