"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface CookiePreferences {
  necessary: boolean;
  analytics: boolean;
  performance: boolean;
}

export default function CookieConsent() {
  const [isVisible, setIsVisible] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [preferences, setPreferences] = useState<CookiePreferences>({
    necessary: true, // Always required
    analytics: false,
    performance: false,
  });

  useEffect(() => {
    // Check if user has already made a choice
    const consent = localStorage.getItem("cookie-consent");
    if (!consent) {
      // Delay showing banner to avoid layout shift
      const timer = setTimeout(() => setIsVisible(true), 1000);
      return () => clearTimeout(timer);
    }
  }, []);

  /* ---------- handlers ---------- */
  const handleAcceptAll = () => {
    saveCookiePreferences({
      necessary: true,
      analytics: true,
      performance: true,
    });
  };

  const handleRejectAll = () => {
    saveCookiePreferences({
      necessary: true,
      analytics: false,
      performance: false,
    });
  };

  const handleSavePreferences = () => {
    saveCookiePreferences(preferences);
  };

  /* ---------- core persistence ---------- */
  const saveCookiePreferences = (prefs: CookiePreferences) => {
    localStorage.setItem("cookie-consent", JSON.stringify(prefs));
    localStorage.setItem("cookie-consent-date", new Date().toISOString());

    // Enable / disable scripts based on preferences
    if (typeof window !== "undefined") {
      if (prefs.analytics) {
        // Vercel Analytics - Initialize queue if not already present
        if (!window.va) {
          window.vaq = window.vaq ?? [];
          window.va = (event, properties) => {
            window.vaq?.push([event, properties]);
          };
        }
      }

      if (prefs.performance) {
        // Vercel Speed Insights - Initialize queue if not already present
        if (!window.si) {
          window.siq = window.siq ?? [];
          window.si = (action, data) => {
            window.siq?.push([action, data]);
          };
        }
      }
    }

    setIsVisible(false);
  };

  const handlePreferenceChange = (type: keyof CookiePreferences) => {
    if (type === "necessary") return; // Can't disable necessary cookies
    setPreferences((prev) => ({ ...prev, [type]: !prev[type] }));
  };

  /* ---------- render ---------- */
  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: 100, opacity: 0 }}
        transition={{ duration: 0.4, ease: "easeOut" }}
        className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t-2 border-purple-500 shadow-2xl"
        role="dialog"
        aria-labelledby="cookie-consent-title"
        aria-describedby="cookie-consent-description"
      >
        <div className="max-w-6xl mx-auto px-4 py-6">
          {!showDetails ? (
            /* ---------- SIMPLE BANNER ---------- */
            <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
              <div className="flex-1">
                <h3
                  id="cookie-consent-title"
                  className="text-lg font-semibold text-indigo-900 mb-2"
                >
                  We use cookies to improve your experience
                </h3>
                <p
                  id="cookie-consent-description"
                  className="text-gray-600 text-sm"
                >
                  We use cookies to analyze site performance and deliver
                  personalized content. You can manage your preferences or learn
                  more about our cookie policy.
                </p>
              </div>
              <div className="flex flex-wrap gap-3">
                <button
                  onClick={() => setShowDetails(true)}
                  className="text-purple-600 hover:text-purple-800 font-medium text-sm underline transition"
                >
                  Customize
                </button>
                <button
                  onClick={handleRejectAll}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 transition font-medium text-sm"
                >
                  Reject All
                </button>
                <button
                  onClick={handleAcceptAll}
                  className="px-6 py-2 bg-gradient-to-r from-purple-500 to-indigo-700 text-white rounded-full hover:scale-105 transition font-semibold text-sm shadow-lg"
                >
                  Accept All
                </button>
              </div>
            </div>
          ) : (
            /* ---------- DETAILED PREFERENCES ---------- */
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-bold text-indigo-900">
                  Cookie Preferences
                </h3>
                <button
                  onClick={() => setShowDetails(false)}
                  className="text-gray-500 hover:text-gray-700 text-2xl"
                  aria-label="Close preferences"
                >
                  ×
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Necessary */}
                <PreferenceCard
                  title="Necessary"
                  description="Essential for website functionality, security, and remembering your preferences. Cannot be disabled."
                  enabled={preferences.necessary}
                  disabled
                />

                {/* Analytics */}
                <PreferenceCard
                  title="Analytics"
                  description="Help us understand how visitors interact with our website through Vercel Analytics. No personal data is collected."
                  enabled={preferences.analytics}
                  onToggle={() => handlePreferenceChange("analytics")}
                />

                {/* Performance */}
                <PreferenceCard
                  title="Performance"
                  description="Monitor site performance and loading times through Vercel Speed Insights to improve user experience."
                  enabled={preferences.performance}
                  onToggle={() => handlePreferenceChange("performance")}
                />
              </div>

              <div className="flex flex-col sm:flex-row gap-3 justify-end">
                <button
                  onClick={handleRejectAll}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 transition font-medium"
                >
                  Reject All
                </button>
                <button
                  onClick={handleSavePreferences}
                  className="px-6 py-2 bg-gradient-to-r from-purple-500 to-indigo-700 text-white rounded-full hover:scale-105 transition font-semibold shadow-lg"
                >
                  Save Preferences
                </button>
              </div>
            </div>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
}

/* ---------- small reusable card component ---------- */
interface PreferenceCardProps {
  title: string;
  description: string;
  enabled: boolean;
  disabled?: boolean;
  onToggle?: () => void;
}
function PreferenceCard({
  title,
  description,
  enabled,
  disabled,
  onToggle,
}: PreferenceCardProps) {
  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <div className="flex items-center justify-between mb-3">
        <h4 className="font-semibold text-indigo-900">{title}</h4>

        {/* toggle */}
        {disabled ? (
          <Toggle visualOnly checked />
        ) : (
          <button
            onClick={onToggle}
            className="relative focus:outline-none focus:ring-2 focus:ring-purple-500 rounded-full"
            aria-pressed={enabled}
          >
            <Toggle checked={enabled} />
          </button>
        )}
      </div>
      <p className="text-sm text-gray-600">{description}</p>
    </div>
  );
}

function Toggle({ checked, visualOnly = false }: { checked: boolean; visualOnly?: boolean }) {
  return (
    <div
      className={`w-12 h-6 rounded-full shadow-inner transition ${checked ? "bg-purple-500" : "bg-gray-300"
        }`}
    >
      <div
        className={`w-6 h-6 bg-white rounded-full shadow transform transition ${checked ? "translate-x-6" : "translate-x-0"
          }`}
      />
      {/* Keep input invisible but accessible for real forms; not needed in this purely visual control */}
      {!visualOnly && (
        <input
          type="checkbox"
          className="sr-only"
          checked={checked}
          readOnly
          tabIndex={-1}
          aria-hidden="true"
        />
      )}
    </div>
  );
}


